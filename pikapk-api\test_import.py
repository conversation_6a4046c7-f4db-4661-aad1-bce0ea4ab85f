#!/usr/bin/env python3
"""
Test script to verify PikPak API import and basic functionality
"""

def test_import():
    """Test importing the PikPak API modules"""
    try:
        from pikpak_api import PikpakApi, PikpakException, DownloadStatus
        print("✓ Import successful!")
        return True
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_api_initialization():
    """Test API initialization"""
    try:
        from pikpak_api import PikpakApi, PikpakException
        
        # Test with username/password
        print("Testing API initialization with username/password...")
        api = Pikpak<PERSON><PERSON>(username='test', password='test')
        print("✓ API instance created successfully")
        
        # Test with encoded token
        print("Testing API initialization with encoded token...")
        api2 = PikpakApi(encoded_token='dGVzdA==')  # base64 encoded 'test'
        print("✓ API instance with token created successfully")
        
        # Test invalid initialization
        print("Testing invalid initialization...")
        try:
            api3 = PikpakApi()
            print("✗ Should have raised exception")
        except PikpakException as e:
            print(f"✓ Expected exception caught: {e}")
        
        return True
    except Exception as e:
        print(f"✗ API initialization test failed: {e}")
        return False

def test_enums():
    """Test enum values"""
    try:
        from pikpak_api import DownloadStatus
        
        print("Testing DownloadStatus enum...")
        print(f"NOT_DOWNLOADING: {DownloadStatus.NOT_DOWNLOADING.value}")
        print(f"DOWNLOADING: {DownloadStatus.DOWNLOADING.value}")
        print(f"DONE: {DownloadStatus.DONE.value}")
        print(f"ERROR: {DownloadStatus.ERROR.value}")
        print(f"NOT_FOUND: {DownloadStatus.NOT_FOUND.value}")
        print("✓ Enum test successful!")
        return True
    except Exception as e:
        print(f"✗ Enum test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=== PikPak API Test Suite ===\n")
    
    tests = [
        ("Import Test", test_import),
        ("API Initialization Test", test_api_initialization),
        ("Enum Test", test_enums),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print(f"=== Test Results: {passed}/{total} passed ===")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed!")

if __name__ == "__main__":
    main()
