"""
PikPak API Usage Example
"""

import os
from pikpak_api import PikpakApi, PikpakException, DownloadStatus


def login_example():
    """
    Example of different login methods
    """
    print("=== Login Examples ===")

    # Method 1: Login with username and password
    username = os.getenv('PIKPAK_USERNAME', 'your_username')
    password = os.getenv('PIKPAK_PASSWORD', 'your_password')

    if username != 'your_username' and password != 'your_password':
        try:
            api = PikpakApi(username=username, password=password)
            api.login()
            print("✓ Login successful!")

            # Get user info
            user_info = api.get_user_info()
            print(f"User ID: {user_info['user_id']}")
            print(f"Encoded Token: {user_info['encoded_token']}")

            return api

        except PikpakException as e:
            print(f"✗ Login failed: {e}")
            return None
    else:
        print("Please set PIKPAK_USERNAME and PIKPAK_PASSWORD environment variables")
        return None


def file_management_example(api):
    """
    Example of file management operations
    """
    if not api:
        return

    print("\n=== File Management Examples ===")

    try:
        # Get file list from root directory
        print("Getting file list...")
        file_list = api.file_list(size=10)
        print(f"✓ Found {len(file_list['files'])} files")

        for i, file in enumerate(file_list['files'][:5]):  # Show first 5 files
            file_type = "📁" if "folder" in file['kind'] else "📄"
            print(f"  {i+1}. {file_type} {file['name']}")

        # Create a new folder (commented out to avoid creating test folders)
        # folder_result = api.create_folder("Test Folder")
        # print(f"✓ Created folder: {folder_result['file']['name']}")

        # Get quota information
        print("\nGetting quota information...")
        quota_info = api.get_quota_info()
        used_gb = int(quota_info['quota']['usage']) / (1024**3)
        total_gb = int(quota_info['quota']['limit']) / (1024**3)
        print(f"✓ Used space: {used_gb:.2f} GB / {total_gb:.2f} GB")

    except PikpakException as e:
        print(f"✗ File management error: {e}")


def offline_download_example(api):
    """
    Example of offline download operations
    """
    if not api:
        return

    print("\n=== Offline Download Examples ===")

    try:
        # Get offline download list
        print("Getting offline download list...")
        offline_list = api.offline_list(size=5)

        if offline_list.get('tasks'):
            print(f"✓ Found {len(offline_list['tasks'])} offline tasks")
            for i, task in enumerate(offline_list['tasks'][:3]):  # Show first 3 tasks
                status_emoji = "⏳" if task['phase'] == 'PHASE_TYPE_RUNNING' else "❌" if task['phase'] == 'PHASE_TYPE_ERROR' else "✅"
                print(f"  {i+1}. {status_emoji} {task['name']} ({task['phase']})")
        else:
            print("✓ No offline download tasks found")

        # Example: Add offline download (commented out)
        # print("\nAdding offline download...")
        # download_result = api.offline_download("https://example.com/file.zip", name="example_file.zip")
        # print(f"✓ Download task created: {download_result['task']['id']}")

    except PikpakException as e:
        print(f"✗ Offline download error: {e}")


def advanced_features_example(api):
    """
    Example of advanced features
    """
    if not api:
        return

    print("\n=== Advanced Features Examples ===")

    try:
        # Get starred files
        print("Getting starred files...")
        starred_files = api.file_star_list(size=5)
        if starred_files.get('files'):
            print(f"✓ Found {len(starred_files['files'])} starred files")
            for file in starred_files['files'][:3]:
                print(f"  ⭐ {file['name']}")
        else:
            print("✓ No starred files found")

        # Get VIP info
        print("\nGetting VIP information...")
        vip_info = api.get_vip_info()
        vip_status = vip_info.get('vip', {}).get('status', 'Not VIP')
        print(f"✓ VIP status: {vip_status}")

        # Get recent events
        print("\nGetting recent events...")
        events = api.events(size=3)
        if events.get('events'):
            print(f"✓ Found {len(events['events'])} recent events")
            for event in events['events'][:2]:
                print(f"  📅 {event.get('action', 'Unknown action')}")
        else:
            print("✓ No recent events found")

        # Get invite code
        print("\nGetting invite code...")
        try:
            invite_code = api.get_invite_code()
            print(f"✓ Invite code: {invite_code}")
        except:
            print("✓ Invite code not available")

    except PikpakException as e:
        print(f"✗ Advanced features error: {e}")


def main():
    """
    Main example function
    """
    print("🚀 PikPak API Python Implementation Example")
    print("=" * 50)

    # Login and get API instance
    api = login_example()

    # Run examples
    file_management_example(api)
    offline_download_example(api)
    advanced_features_example(api)

    print("\n" + "=" * 50)
    print("✨ Example completed!")
    print("\nNote: To use with real credentials, set environment variables:")
    print("  PIKPAK_USERNAME=your_username")
    print("  PIKPAK_PASSWORD=your_password")
if __name__ == "__main__":
    main()
