# PikPak API Python Implementation

这是一个 PikPak API 的 Python 实现，从 TypeScript 版本重写而来。提供了完整的 PikPak 云存储服务 API 功能。

## 功能特性

- 用户认证（用户名/密码登录，Token 刷新）
- 文件管理（列表、创建文件夹、重命名、删除等）
- 离线下载管理
- 文件分享
- 批量操作（移动、复制、加星标等）
- 路径操作（路径转 ID，批量移动/复制）
- 用户信息和配额查询

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 基本用法

```python
from pikpak_api import PikpakApi, PikpakException

# 方法1：使用用户名和密码登录
api = PikpakApi(username="your_username", password="your_password")
api.login()

# 方法2：使用已编码的 token（推荐用于后续使用）
# api = PikpakApi(encoded_token="your_encoded_token")

# 获取用户信息
user_info = api.get_user_info()
print(f"用户ID: {user_info['user_id']}")

# 获取文件列表
file_list = api.file_list(size=10)
for file in file_list['files']:
    print(f"文件: {file['name']}")
```

### 文件操作

```python
# 创建文件夹
folder = api.create_folder("新文件夹")

# 获取文件下载链接
download_info = api.get_download_url("file_id")
download_url = download_info['web_content_link']

# 重命名文件
api.file_rename("file_id", "新文件名.txt")

# 移动文件到回收站
api.delete_to_trash(["file_id1", "file_id2"])
```

### 离线下载

```python
# 添加离线下载任务
download_task = api.offline_download("https://example.com/file.zip")
task_id = download_task['task']['id']

# 获取离线下载列表
offline_list = api.offline_list()

# 检查任务状态
status = api.get_task_status(task_id, "file_id")
```

### 批量操作

```python
# 批量移动文件
api.file_batch_move(["file_id1", "file_id2"], "target_folder_id")

# 批量复制文件
api.file_batch_copy(["file_id1", "file_id2"], "target_folder_id")

# 批量加星标
api.file_batch_star(["file_id1", "file_id2"])

# 批量分享文件
share_result = api.file_batch_share(["file_id1", "file_id2"])
```

### 路径操作

```python
# 将路径转换为文件ID
path_ids = api.path_to_id("/Movies/Action", create=True)

# 根据路径移动文件
api.file_move_or_copy_by_path(
    ["/source/file1.mp4", "/source/file2.mp4"],
    "/target/folder",
    move=True,
    create=True
)
```

## API 方法列表

### 认证相关
- `login()` - 用户登录
- `get_user_info()` - 获取用户信息
- `captcha_init()` - 初始化验证码

### 文件管理
- `file_list()` - 获取文件列表
- `create_folder()` - 创建文件夹
- `file_rename()` - 重命名文件
- `get_download_url()` - 获取下载链接
- `delete_to_trash()` - 移动到回收站
- `untrash()` - 从回收站恢复
- `delete_forever()` - 永久删除

### 批量操作
- `file_batch_move()` - 批量移动文件
- `file_batch_copy()` - 批量复制文件
- `file_batch_star()` - 批量加星标
- `file_batch_unstar()` - 批量取消星标
- `file_batch_share()` - 批量分享文件

### 离线下载
- `offline_download()` - 添加离线下载
- `offline_list()` - 获取离线下载列表
- `get_task_status()` - 获取任务状态
- `offline_file_info()` - 获取离线文件信息
- `offline_task_retry()` - 重试离线任务
- `delete_tasks()` - 删除任务

### 其他功能
- `path_to_id()` - 路径转ID
- `file_move_or_copy_by_path()` - 根据路径移动/复制
- `file_star_list()` - 获取星标文件列表
- `events()` - 获取最近事件
- `get_quota_info()` - 获取配额信息
- `get_vip_info()` - 获取VIP信息
- `get_transfer_quota()` - 获取传输配额
- `get_invite_code()` - 获取邀请码

## 错误处理

```python
from pikpak_api import PikpakException

try:
    api = PikpakApi(username="user", password="pass")
    api.login()
except PikpakException as e:
    print(f"PikPak API 错误: {e}")
except Exception as e:
    print(f"其他错误: {e}")
```

## 注意事项

1. 首次使用需要用户名和密码登录，登录成功后可以保存 `encoded_token` 用于后续认证
2. API 会自动处理 token 刷新
3. 所有文件操作都支持批量处理
4. 路径操作支持自动创建不存在的文件夹
5. 建议在生产环境中妥善保管认证信息

## 许可证

本项目基于原 TypeScript 版本重写，遵循相同的许可证条款。
