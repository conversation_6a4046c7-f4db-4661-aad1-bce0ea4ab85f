"""
PikPak API Python Implementation
"""

import json
import base64
from typing import Optional, Dict, Any, List
import requests
from urllib.parse import urlencode

try:
    from .exceptions import PikpakException
    from .enums import DownloadStatus
    from .models import FileR<PERSON>ord, FileList, TokenData, File
except ImportError:
    from exceptions import PikpakException
    from enums import DownloadStatus
    from models import FileRecord, FileList, TokenData, File


class PikpakApi:
    """
    PikPak API client class
    """
    
    PIKPAK_API_HOST = "api-drive.mypikpak.com"
    PIKPAK_USER_HOST = "user.mypikpak.com"
    
    CLIENT_ID = "YNxT9w7GMdWvEOKa"
    CLIENT_SECRET = "dbw2OtmVEeuUvIptb1Coyg"
    
    def __init__(
        self,
        username: Optional[str] = None,
        password: Optional[str] = None,
        encoded_token: Optional[str] = None,
        session_kwargs: Optional[Dict[str, Any]] = None
    ):
        """
        Create a PikpakApi instance
        
        Args:
            username: <PERSON><PERSON><PERSON>k username
            password: Pikpak password
            encoded_token: Encoded token string containing access and refresh tokens
            session_kwargs: Optional requests session configuration parameters
        """
        self.username = username
        self.password = password
        self.encoded_token = encoded_token
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.user_id: Optional[str] = None
        self.session = requests.Session()
        self.path_id_cache: Dict[str, FileRecord] = {}
        self.device_id = "01J0NP4CPJR3R9XHGZZKTCFAET"
        
        if session_kwargs:
            for key, value in session_kwargs.items():
                setattr(self.session, key, value)
        
        if self.encoded_token:
            self._decode_token()
        elif self.username and self.password:
            # Do nothing, wait for login
            pass
        else:
            raise PikpakException("Must provide username and password, or encoded token string")
    
    def _get_headers(self, access_token: Optional[str] = None) -> Dict[str, str]:
        """Get request headers"""
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36",
            "Content-Type": "application/json; charset=utf-8",
        }
        
        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        elif access_token:
            headers["Authorization"] = f"Bearer {access_token}"
        
        return headers
    
    def _make_request(
        self,
        method: str,
        url: str,
        data: Optional[Any] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        retry: int = 0
    ) -> Any:
        """Make HTTP request with retry logic"""
        try:
            request_headers = headers or self._get_headers()
            
            response = self.session.request(
                method=method,
                url=url,
                json=data if data and method.upper() != 'GET' else None,
                params=params,
                headers=request_headers
            )
            
            response.raise_for_status()
            json_data = response.json()
            
            if "error" in json_data:
                if json_data.get("error_code") == 16 and retry < 3:
                    self._refresh_access_token()
                    return self._make_request(method, url, data, params, headers, retry + 1)
                else:
                    raise PikpakException(json_data.get("error_description", "Unknown error"))
            
            return json_data
            
        except requests.RequestException as e:
            raise PikpakException(str(e))
    
    def _request_get(self, url: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """Make GET request"""
        return self._make_request("GET", url, params=params)
    
    def _request_post(
        self,
        url: str,
        data: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Any:
        """Make POST request"""
        return self._make_request("POST", url, data=data, headers=headers)
    
    def _request_patch(self, url: str, data: Optional[Any] = None) -> Any:
        """Make PATCH request"""
        return self._make_request("PATCH", url, data=data)
    
    def _request_delete(
        self,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Any] = None
    ) -> Any:
        """Make DELETE request"""
        return self._make_request("DELETE", url, data=data, params=params)
    
    def _decode_token(self) -> None:
        """Decode encoded token"""
        try:
            decoded_data = json.loads(base64.b64decode(self.encoded_token).decode('utf-8'))
            self.access_token = decoded_data["access_token"]
            self.refresh_token = decoded_data["refresh_token"]
        except Exception:
            raise PikpakException("Invalid encoded token string")
    
    def _encode_token(self) -> None:
        """Encode token data"""
        token_data = {
            "access_token": self.access_token,
            "refresh_token": self.refresh_token,
        }
        self.encoded_token = base64.b64encode(json.dumps(token_data).encode('utf-8')).decode('utf-8')
    
    def captcha_init(self) -> Any:
        """
        Initialize captcha
        
        Returns:
            Dict containing initialization result
        """
        url = f"https://{self.PIKPAK_USER_HOST}/v1/shield/captcha/init"
        params = {
            "client_id": self.CLIENT_ID,
            "action": "POST:/v1/auth/signin",
            "device_id": self.device_id,
            "meta": {"email": self.username},
        }
        return self._request_post(url, params)
    
    def login(self) -> None:
        """
        Login to Pikpak using username and password
        """
        login_url = f"https://{self.PIKPAK_USER_HOST}/v1/auth/token"
        login_data = {
            "client_id": self.CLIENT_ID,
            "client_secret": self.CLIENT_SECRET,
            "password": self.password,
            "username": self.username,
            "grant_type": "password",
        }
        
        try:
            # Convert to form data for login
            headers = {"Content-Type": "application/x-www-form-urlencoded"}
            response = self.session.post(
                login_url,
                data=urlencode(login_data),
                headers=headers
            )
            response.raise_for_status()
            user_info = response.json()
            
            self.access_token = user_info["access_token"]
            self.refresh_token = user_info["refresh_token"]
            self.user_id = user_info["sub"]
            self._encode_token()
        except Exception:
            raise PikpakException("Login failed, please check username and password")
    
    def _refresh_access_token(self) -> None:
        """
        Refresh access token
        """
        refresh_url = f"https://{self.PIKPAK_USER_HOST}/v1/auth/token"
        refresh_data = {
            "client_id": self.CLIENT_ID,
            "refresh_token": self.refresh_token,
            "grant_type": "refresh_token",
        }
        
        try:
            user_info = self._request_post(refresh_url, refresh_data)
            self.access_token = user_info["access_token"]
            self.refresh_token = user_info["refresh_token"]
            self.user_id = user_info["sub"]
            self._encode_token()
        except Exception:
            raise PikpakException("Failed to refresh access token")
    
    def get_user_info(self) -> Dict[str, Optional[str]]:
        """
        Get user information

        Returns:
            Dict containing user information
        """
        return {
            "username": self.username,
            "user_id": self.user_id,
            "access_token": self.access_token,
            "refresh_token": self.refresh_token,
            "encoded_token": self.encoded_token,
        }

    def create_folder(self, name: str = "新建文件夹", parent_id: Optional[str] = None) -> Any:
        """
        Create folder

        Args:
            name: Folder name, default is "新建文件夹"
            parent_id: Parent folder ID, default is root directory

        Returns:
            Dict containing creation result
        """
        url = f"https://{self.PIKPAK_API_HOST}/drive/v1/files"
        data = {
            "kind": "drive#folder",
            "name": name,
            "parent_id": parent_id,
        }
        return self._request_post(url, data)

    def delete_to_trash(self, ids: List[str]) -> Any:
        """
        Move files or folders to trash

        Args:
            ids: List of file or folder IDs to move to trash

        Returns:
            Dict containing operation result
        """
        url = f"https://{self.PIKPAK_API_HOST}/drive/v1/files:batchTrash"
        data = {"ids": ids}
        return self._request_post(url, data)

    def untrash(self, ids: List[str]) -> Any:
        """
        Remove files or folders from trash

        Args:
            ids: List of file or folder IDs to remove from trash

        Returns:
            Dict containing operation result
        """
        url = f"https://{self.PIKPAK_API_HOST}/drive/v1/files:batchUntrash"
        data = {"ids": ids}
        return self._request_post(url, data)

    def delete_forever(self, ids: List[str]) -> Any:
        """
        Permanently delete files or folders

        Args:
            ids: List of file or folder IDs to permanently delete

        Returns:
            Dict containing operation result
        """
        url = f"https://{self.PIKPAK_API_HOST}/drive/v1/files:batchDelete"
        data = {"ids": ids}
        return self._request_post(url, data)

    def offline_download(
        self,
        file_url: str,
        parent_id: Optional[str] = None,
        name: Optional[str] = None
    ) -> Any:
        """
        Offline download file

        Args:
            file_url: File URL
            parent_id: Parent folder ID, defaults to My Pack if not provided
            name: File name, defaults to filename from URL if not provided

        Returns:
            Dict containing operation result
        """
        url = f"https://{self.PIKPAK_API_HOST}/drive/v1/files"
        download_data = {
            "kind": "drive#file",
            "name": name,
            "upload_type": "UPLOAD_TYPE_URL",
            "url": {"url": file_url},
            "folder_type": "" if parent_id else "DOWNLOAD",
            "parent_id": parent_id,
        }
        return self._request_post(url, download_data)

    def offline_list(
        self,
        size: int = 10000,
        next_page_token: Optional[str] = None,
        phase: List[str] = None
    ) -> Any:
        """
        Get offline download list

        Args:
            size: Number of items per request, default is 10000
            next_page_token: Next page token
            phase: Offline download task status, default is ["PHASE_TYPE_RUNNING", "PHASE_TYPE_ERROR"]
                   Supported values: PHASE_TYPE_RUNNING, PHASE_TYPE_ERROR, PHASE_TYPE_COMPLETE, PHASE_TYPE_PENDING

        Returns:
            Dict containing operation result
        """
        if phase is None:
            phase = ["PHASE_TYPE_RUNNING", "PHASE_TYPE_ERROR"]

        url = f"https://{self.PIKPAK_API_HOST}/drive/v1/tasks"
        data = {
            "type": "offline",
            "thumbnail_size": "SIZE_SMALL",
            "limit": size,
            "page_token": next_page_token,
            "filters": json.dumps({"phase": {"in": ",".join(phase)}}),
            "with": "reference_resource",
        }
        return self._request_get(url, data)

    def get_task_status(self, task_id: str, file_id: str) -> DownloadStatus:
        """
        Get offline download task status

        Args:
            task_id: Offline download task ID
            file_id: Offline download file ID

        Returns:
            DownloadStatus representing download status
        """
        try:
            infos = self.offline_list()
            if infos and "tasks" in infos:
                for task in infos["tasks"]:
                    if task_id == task["id"]:
                        return DownloadStatus.DOWNLOADING

            file_info = self.offline_file_info(file_id)
            if file_info:
                return DownloadStatus.DONE
            else:
                return DownloadStatus.NOT_FOUND
        except Exception:
            return DownloadStatus.ERROR

    def offline_file_info(self, file_id: str) -> Any:
        """
        Get offline download file information

        Args:
            file_id: Offline download file ID

        Returns:
            Dict containing operation result
        """
        url = f"https://{self.PIKPAK_API_HOST}/drive/v1/files/{file_id}"
        return self._request_get(url, {"thumbnail_size": "SIZE_LARGE"})

    def file_list(
        self,
        size: int = 0,
        parent_id: Optional[str] = None,
        next_page_token: Optional[str] = None,
        additional_filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Get file list

        Args:
            size: Number of items per request, default is 0 (all)
            parent_id: Parent folder ID, default is root directory
            next_page_token: Next page token
            additional_filters: Additional filter conditions

        Returns:
            Dict containing file list
        """
        default_filters = {
            "trashed": {"eq": False},
            "phase": {"eq": "PHASE_TYPE_COMPLETE"},
        }
        filters = {**default_filters, **(additional_filters or {})}

        list_url = f"https://{self.PIKPAK_API_HOST}/drive/v1/files"
        list_data = {
            "parent_id": parent_id,
            "thumbnail_size": "SIZE_MEDIUM",
            "limit": size,
            "with_audit": "true",
            "page_token": next_page_token,
            "filters": json.dumps(filters),
        }

        return self._request_get(list_url, list_data)

    def events(self, size: int = 100, next_page_token: Optional[str] = None) -> Any:
        """
        Get recent events list

        Args:
            size: Number of items per request, default is 100, set to 0 for all
            next_page_token: Next page token

        Returns:
            Dict containing operation result
        """
        url = f"https://{self.PIKPAK_API_HOST}/drive/v1/events"
        data = {
            "thumbnail_size": "SIZE_MEDIUM",
            "limit": size,
            "next_page_token": next_page_token,
        }
        return self._request_get(url, data)

    def offline_task_retry(self, task_id: str) -> Any:
        """
        Retry offline download task

        Args:
            task_id: Offline download task ID

        Returns:
            Dict containing operation result

        Raises:
            PikpakException: When retry offline download task fails
        """
        url = f"https://{self.PIKPAK_API_HOST}/drive/v1/task"
        data = {
            "type": "offline",
            "create_type": "RETRY",
            "id": task_id,
        }
        try:
            return self._request_post(url, data)
        except Exception as e:
            raise PikpakException(f"Failed to retry offline download task: {task_id}. {e}")

    def delete_tasks(self, task_ids: List[str], delete_files: bool = False) -> None:
        """
        Delete tasks by task IDs

        Args:
            task_ids: List of task IDs to delete
            delete_files: Whether to delete files as well, default is False

        Raises:
            PikpakException: When delete tasks fails
        """
        url = f"https://{self.PIKPAK_API_HOST}/drive/v1/tasks"
        params = {
            "task_ids": task_ids,
            "delete_files": delete_files,
        }
        try:
            self._request_delete(url, params)
        except Exception as e:
            raise PikpakException(f"Failed to delete tasks: {task_ids}. {e}")

    def path_to_id(self, path: str, create: bool = False) -> List[FileRecord]:
        """
        Convert path like /path/a/b to folder IDs

        Args:
            path: Path string
            create: Whether to create non-existent folders

        Returns:
            List of folder IDs
        """
        if not path or len(path) <= 0:
            return []

        paths = [p for p in path.split("/") if p.strip()]

        # Build multi-level path expressions to find the closest level to target
        multi_level_paths = [f"/{'/'.join(paths[:i+1])}" for i in range(len(paths))]
        path_ids = [
            self.path_id_cache[p] for p in multi_level_paths
            if p in self.path_id_cache
        ]

        # Check cache hit situation
        hit_count = len(path_ids)

        if hit_count == len(paths):
            return path_ids

        count = hit_count
        parent_id = path_ids[hit_count - 1].id if hit_count > 0 else None
        next_page_token = None

        while count < len(paths):
            data = self.file_list(0, parent_id, next_page_token)

            record_of_target_path = None

            for f in data["files"]:
                current_path = f"/{'/'.join(paths[:count] + [f['name']])}"
                file_type = "folder" if "folder" in f["kind"] else "file"
                record = FileRecord(
                    id=f["id"],
                    name=f["name"],
                    file_type=file_type,
                )

                self.path_id_cache[current_path] = record

                if f["name"] == paths[count]:
                    record_of_target_path = record

            if record_of_target_path:
                path_ids.append(record_of_target_path)
                count += 1
                parent_id = record_of_target_path.id
            elif data.get("next_page_token") and (
                not next_page_token or next_page_token != data["next_page_token"]
            ):
                next_page_token = data["next_page_token"]
            elif create:
                try:
                    create_result = self.create_folder(paths[count], parent_id)
                    file_id = create_result["file"]["id"]
                    record = FileRecord(
                        id=file_id,
                        name=paths[count],
                        file_type="folder",
                    )

                    path_ids.append(record)
                    current_path = f"/{'/'.join(paths[:count+1])}"
                    self.path_id_cache[current_path] = record
                    count += 1
                    parent_id = file_id
                except Exception as e:
                    print(f"Failed to create folder {paths[count]}: {e}")
                    break
            else:
                break

        return path_ids

    def file_batch_move(
        self,
        ids: List[str],
        to_parent_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Batch move files

        Args:
            ids: List of file IDs
            to_parent_id: Target folder ID, default is root directory

        Returns:
            API response data
        """
        to = {"parent_id": to_parent_id} if to_parent_id else {}
        return self._request_post(
            f"https://{self.PIKPAK_API_HOST}/drive/v1/files:batchMove",
            {"ids": ids, "to": to}
        )

    def file_batch_copy(
        self,
        ids: List[str],
        to_parent_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Batch copy files

        Args:
            ids: List of file IDs
            to_parent_id: Target folder ID, default is root directory

        Returns:
            Pikpak API result
        """
        to = {"parent_id": to_parent_id} if to_parent_id else {}
        return self._request_post(
            f"https://{self.PIKPAK_API_HOST}/drive/v1/files:batchCopy",
            {"ids": ids, "to": to}
        )

    def file_move_or_copy_by_path(
        self,
        from_paths: List[str],
        to_path: str,
        move: bool = False,
        create: bool = False
    ) -> Any:
        """
        Move or copy files by path

        Args:
            from_paths: List of file paths to move or copy
            to_path: Path to move or copy to
            move: Whether to move, default is copy
            create: Whether to create non-existent folders, default is False

        Returns:
            Pikpak API result
        """
        from_ids = []
        for path in from_paths:
            path_ids = self.path_to_id(path)
            if path_ids:
                last_path_id = path_ids[-1]
                if last_path_id.id:
                    from_ids.append(last_path_id.id)

        if not from_ids:
            raise PikpakException("Files to move or copy do not exist")

        to_path_ids = self.path_to_id(to_path, create)
        to_parent_id = to_path_ids[-1].id if to_path_ids else None

        if move:
            return self.file_batch_move(from_ids, to_parent_id)
        else:
            return self.file_batch_copy(from_ids, to_parent_id)

    def get_download_url(self, file_id: str) -> Any:
        """
        Get file download link

        Args:
            file_id: File ID

        Returns:
            Object containing file details

        Note:
            - Use `medias[0].link.url` for high-speed streaming in media services or tools.
            - Use `web_content_link` to download files.
        """
        return self._request_get(
            f"https://{self.PIKPAK_API_HOST}/drive/v1/files/{file_id}?_magic=2021&thumbnail_size=SIZE_LARGE"
        )

    def file_rename(self, file_id: str, new_file_name: str) -> Any:
        """
        Rename file

        Args:
            file_id: File ID
            new_file_name: New file name

        Returns:
            Updated file information
        """
        data = {"name": new_file_name}

        try:
            return self._request_patch(
                f"https://{self.PIKPAK_API_HOST}/drive/v1/files/{file_id}",
                data
            )
        except Exception:
            raise PikpakException("File rename failed")

    def file_batch_star(self, ids: List[str]) -> Any:
        """
        Batch star files

        Args:
            ids: List of file IDs

        Returns:
            Pikpak API result
        """
        data = {"ids": ids}
        return self._request_post(
            f"https://{self.PIKPAK_API_HOST}/drive/v1/files:star",
            data
        )

    def file_batch_unstar(self, ids: List[str]) -> Any:
        """
        Batch unstar files

        Args:
            ids: List of file IDs

        Returns:
            Pikpak API result
        """
        data = {"ids": ids}
        return self._request_post(
            f"https://{self.PIKPAK_API_HOST}/drive/v1/files:unstar",
            data
        )

    def file_star_list(
        self,
        size: int = 100,
        next_page_token: Optional[str] = None
    ) -> Any:
        """
        Get starred files list

        Args:
            size: Number of items per request, default is 100
            next_page_token: Next page token for getting more results

        Returns:
            Pikpak API result containing starred files list
        """
        additional_filters = {"system_tag": {"in": "STAR"}}
        return self.file_list(size, "*", next_page_token, additional_filters)

    def file_batch_share(
        self,
        ids: List[str],
        need_password: bool = False,
        expiration_days: int = -1
    ) -> Any:
        """
        Batch share files

        Args:
            ids: List of file IDs
            need_password: Whether share password is required, default is False
            expiration_days: Share link validity days, default is -1 (permanent)

        Returns:
            Pikpak API result containing share link information
        """
        data = {
            "file_ids": ids,
            "share_to": "encryptedlink" if need_password else "publiclink",
            "expiration_days": expiration_days,
            "pass_code_option": "REQUIRED" if need_password else "NOT_REQUIRED",
        }
        return self._request_post(
            f"https://{self.PIKPAK_API_HOST}/drive/v1/share",
            data
        )

    def get_quota_info(self) -> Any:
        """
        Get current user's space quota information

        Returns:
            Pikpak API result containing space quota information
        """
        return self._request_get(
            f"https://{self.PIKPAK_API_HOST}/drive/v1/about"
        )

    def get_invite_code(self) -> str:
        """
        Get invite code

        Returns:
            Invite code string
        """
        result = self._request_get(
            f"https://{self.PIKPAK_API_HOST}/vip/v1/activity/inviteCode"
        )
        return result["code"]

    def get_vip_info(self) -> Any:
        """
        Get VIP information

        Returns:
            Pikpak API result containing VIP information
        """
        return self._request_get(
            f"https://{self.PIKPAK_API_HOST}/drive/v1/privilege/vip"
        )

    def get_transfer_quota(self) -> Any:
        """
        Get transfer quota information

        Returns:
            Pikpak API result containing transfer quota information
        """
        url = f"https://{self.PIKPAK_API_HOST}/vip/v1/quantity/list?type=transfer"
        return self._request_get(url)
