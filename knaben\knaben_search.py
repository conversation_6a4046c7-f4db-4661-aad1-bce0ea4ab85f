#!/usr/bin/env python3
"""
Knaben API Search Tool
A command-line tool to search torrents using the Knaben API
"""

import requests
import json
import argparse
import sys
from typing import Dict, List, Optional
from datetime import datetime


class KnabenAPI:
    """Knaben API client for searching torrents"""
    
    def __init__(self):
        self.base_url = "https://api.knaben.org/v1"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'KnabenSearchTool/1.0',
            'Content-Type': 'application/json'
        })
    
    def search(self, 
               query: str,
               search_type: str = "score",
               search_field: Optional[str] = None,
               order_by: str = "seeders",
               order_direction: str = "desc",
               categories: Optional[List[int]] = None,
               from_offset: int = 0,
               size: int = 50,
               hide_unsafe: bool = False,
               hide_xxx: bool = False,
               seconds_since_last_seen: Optional[int] = None) -> Dict:
        """
        Search torrents using Knaben API
        
        Args:
            query: Search query string
            search_type: "score" or percentage value (e.g., "100%")
            search_field: Field to search in (e.g., "title")
            order_by: Field to order by (e.g., "seeders", "peers", "date")
            order_direction: "desc" or "asc"
            categories: List of category IDs
            from_offset: Pagination offset
            size: Number of results (max 300)
            hide_unsafe: Filter out potentially unsafe results
            hide_xxx: Hide adult content
            seconds_since_last_seen: Filter by last seen time
            
        Returns:
            API response as dictionary
        """
        
        # Prepare request payload
        payload = {
            "query": query,
            "search_type": search_type,
            "order_by": order_by,
            "order_direction": order_direction,
            "from": from_offset,
            "size": min(size, 300),  # Enforce max size
            "hide_unsafe": hide_unsafe,
            "hide_xxx": hide_xxx
        }
        
        # Add optional parameters
        if search_field:
            payload["search_field"] = search_field
        if categories:
            payload["categories"] = categories
        if seconds_since_last_seen:
            payload["seconds_since_last_seen"] = seconds_since_last_seen
        
        try:
            response = self.session.post(self.base_url, json=payload, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error making request: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"Error parsing response: {e}")
            return None
    
    def get_recent(self, size: int = 150) -> Dict:
        """
        Get recent torrents without any search query
        
        Args:
            size: Number of results to return
            
        Returns:
            API response as dictionary
        """
        try:
            response = self.session.get(self.base_url, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error making request: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"Error parsing response: {e}")
            return None


def format_size(bytes_size: int) -> str:
    """Convert bytes to human readable format"""
    if bytes_size == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    size = float(bytes_size)
    unit_index = 0
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    return f"{size:.1f} {units[unit_index]}"


def format_date(date_str: str) -> str:
    """Format date string for display"""
    try:
        # Handle different date formats
        if 'T' in date_str:
            if '+' in date_str:
                dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            else:
                dt = datetime.fromisoformat(date_str.replace('Z', ''))
        else:
            dt = datetime.fromisoformat(date_str)
        return dt.strftime('%Y-%m-%d')
    except:
        return date_str


def display_results(results: Dict):
    """Display search results in a formatted way"""
    if not results or 'hits' not in results:
        print("No results found or invalid response")
        return
    
    hits = results['hits']
    total = results.get('total', {}).get('value', 0)
    
    print(f"\nFound {total} results:")
    print("=" * 80)
    
    for i, hit in enumerate(hits, 1):
        title = hit.get('title', 'Unknown')
        size = format_size(hit.get('bytes', 0))
        seeders = hit.get('seeders', 0)
        peers = hit.get('peers', 0)
        date = format_date(hit.get('date', ''))
        tracker = hit.get('tracker', 'Unknown')
        category = hit.get('category', 'Unknown')
        
        print(f"{i:2d}. {title}")
        print(f"    Size: {size} | S: {seeders} | P: {peers} | Date: {date}")
        print(f"    Tracker: {tracker} | Category: {category}")

        # Show category IDs for debugging
        category_ids = hit.get('categoryId', [])
        if category_ids:
            print(f"    Category IDs: {category_ids}")

        # Show magnet link if available
        if hit.get('magnetUrl'):
            magnet = hit['magnetUrl']
            if len(magnet) > 100:
                magnet = magnet[:97] + "..."
            print(f"    Magnet: {magnet}")
        elif hit.get('link'):
            link = hit['link']
            if len(link) > 100:
                link = link[:97] + "..."
            print(f"    Link: {link}")
        
        print()


def load_config():
    """Load configuration from config.json"""
    import json
    import os

    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {
            "default_settings": {
                "size": 40,
                "order_by": "seeders",
                "order_direction": "desc",
                "hide_unsafe": False,
                "hide_xxx": False
            }
        }
    except json.JSONDecodeError:
        return {
            "default_settings": {
                "size": 40,
                "order_by": "seeders",
                "order_direction": "desc",
                "hide_unsafe": False,
                "hide_xxx": False
            }
        }


def main():
    config = load_config()
    default_settings = config.get('default_settings', {})

    parser = argparse.ArgumentParser(description='Search torrents using Knaben API')
    parser.add_argument('query', nargs='?', help='Search query')
    parser.add_argument('--recent', action='store_true', help='Get recent torrents without search')
    parser.add_argument('--size', type=int, default=default_settings.get('size', 40),
                       help=f'Number of results (default: {default_settings.get("size", 40)}, max: 300)')
    parser.add_argument('--order-by', default=default_settings.get('order_by', 'seeders'),
                       help=f'Order by field (default: {default_settings.get("order_by", "seeders")})')
    parser.add_argument('--order-direction', choices=['desc', 'asc'],
                       default=default_settings.get('order_direction', 'desc'), help='Order direction')
    parser.add_argument('--search-field', default='title', help='Search in specific field (default: title)')
    parser.add_argument('--categories', nargs='+', type=int, help='Category IDs to filter by')
    parser.add_argument('--show-unsafe', action='store_true', help='Show potentially unsafe results')
    parser.add_argument('--show-xxx', action='store_true', help='Show adult content')
    parser.add_argument('--hours', type=int, help='Only show results seen in last N hours')
    
    args = parser.parse_args()
    
    if not args.query and not args.recent:
        print("Please provide a search query or use --recent flag")
        parser.print_help()
        sys.exit(1)
    
    api = KnabenAPI()
    
    if args.recent:
        print("Fetching recent torrents...")
        results = api.get_recent(args.size)
    else:
        print(f"Searching for: {args.query}")
        
        # Convert hours to seconds if specified
        seconds_since_last_seen = None
        if args.hours:
            seconds_since_last_seen = args.hours * 3600

        # Auto-include adult content categories if not specified
        categories = args.categories
        if not categories:
            # Default to include all adult content categories
            categories = [5000000, 5001000, 6000000, 6001000]

        results = api.search(
            query=args.query,
            search_type="75%",  # Set to 75% as requested
            search_field=args.search_field,
            order_by=args.order_by,
            order_direction=args.order_direction,
            categories=categories,
            size=args.size,
            hide_unsafe=False if args.show_unsafe else default_settings.get('hide_unsafe', False),
            hide_xxx=False if args.show_xxx else default_settings.get('hide_xxx', False),
            seconds_since_last_seen=seconds_since_last_seen
        )
    
    if results:
        display_results(results)
    else:
        print("Failed to get results from API")
        sys.exit(1)


if __name__ == "__main__":
    main()
