"""
PikPak API Data Models
"""

from typing import List, Optional, Any, Dict
from dataclasses import dataclass


@dataclass
class File:
    """File information model"""
    kind: str                           # File type, e.g., "drive#folder" for folder
    id: str                            # File ID
    parent_id: str                     # Parent folder ID, "" for root directory
    name: str                          # File name
    user_id: str                       # User ID
    size: str                          # File size in bytes (string type)
    revision: str                      # File revision number
    file_extension: str                # File extension
    mime_type: str                     # File MIME type
    starred: bool                      # Whether starred
    web_content_link: str              # File web content link
    created_time: str                  # Creation time, ISO 8601 format
    modified_time: str                 # Modification time, ISO 8601 format
    icon_link: str                     # File icon link
    thumbnail_link: str                # File thumbnail link
    md5_checksum: str                  # File MD5 checksum
    hash: str                          # File hash value
    links: Dict[str, Any]              # File link information
    phase: str                         # File status, e.g., "PHASE_TYPE_COMPLETE"
    audit: Optional[Any]               # File audit information
    medias: List[Any]                  # File media information
    trashed: bool                      # Whether in trash
    delete_time: str                   # Delete time, ISO 8601 format
    original_url: str                  # File original link
    params: List[Any]                  # File parameter information
    original_file_index: int           # File original index
    space: str                         # File space information
    apps: List[Any]                    # File application information
    writable: bool                     # Whether writable
    folder_type: str                   # Folder type, e.g., "DOWNLOAD"
    collection: Optional[Any]          # File collection information
    sort_name: str                     # File sort name
    user_modified_time: str            # User modification time, ISO 8601 format
    spell_name: List[str]              # File spell name
    file_category: str                 # File category, e.g., "OTHER"
    tags: List[Any]                    # File tag information
    reference_events: List[Any]        # File reference events
    reference_resource: Optional[Any]  # File reference resource


@dataclass
class FileList:
    """File list model"""
    kind: str                          # List type, e.g., "drive#fileList"
    next_page_token: str               # Next page token, empty if last page
    files: List[File]                  # File list
    version: str                       # Version information
    version_outdated: bool             # Whether version is outdated
    sync_time: str                     # Sync time, ISO 8601 format


@dataclass
class TokenData:
    """Token data model"""
    access_token: str
    refresh_token: str


@dataclass
class FileRecord:
    """File record model for path caching"""
    id: str
    name: str
    file_type: str
